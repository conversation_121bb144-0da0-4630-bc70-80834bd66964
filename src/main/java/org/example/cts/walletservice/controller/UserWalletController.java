package org.example.cts.walletservice.controller;

import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.cts.commonauth.AllUserDetails;
import org.example.cts.walletservice.config.Constants;
import org.example.cts.walletservice.model.dto.CreateWalletRequest;
import org.example.cts.walletservice.model.dto.UserPortfolioWalletDto;
import org.example.cts.walletservice.model.entity.UserWallet;
import org.example.cts.walletservice.service.UserWalletService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/**
 * The type User wallet controller.
 */
@RestController
@RequestMapping(Constants.BASE_URL)
@RequiredArgsConstructor
public class UserWalletController {

    private final UserWalletService userWalletService;

    /**
     * Create wallet user wallet.
     *
     * @param request
     *            the request
     * @return the user wallet
     */
    @Deprecated(since = "Use gRPC instead. Wallet should not be created directly")
    @PostMapping("/new")
    public UserWallet createWallet(@RequestBody CreateWalletRequest request) {
        return userWalletService.createWallet(request);
    }

    /**
     * Gets wallet.
     *
     * @param walletId
     *            the wallet id
     * @return the wallet
     */
    @GetMapping("/{walletId}")
    public UserWallet getWallet(@PathVariable UUID walletId) {
        return userWalletService.getWallet(walletId);
    }

    @GetMapping("/my")
    public ResponseEntity<UserPortfolioWalletDto> getMyWallet(@AuthenticationPrincipal AllUserDetails userDetails) {
        return ResponseEntity.ok(userWalletService.getWalletByUserId(userDetails.getId()));
    }
}
