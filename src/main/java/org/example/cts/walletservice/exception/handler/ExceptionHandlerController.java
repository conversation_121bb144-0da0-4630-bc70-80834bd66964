package org.example.cts.walletservice.exception.handler;

import java.util.concurrent.CompletionException;

import org.cts.commonauth.exceptions.GeneralError;
import org.example.cts.walletservice.exception.NoDataFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

/**
 * The type Exception handler controller.
 */
@RestControllerAdvice
public class ExceptionHandlerController {

    /**
     * Custom runtime string.
     *
     * @param exception
     *            the exception
     * @return the string
     */
    @ExceptionHandler(CompletionException.class)
    public ResponseEntity<GeneralError> customRuntime(CompletionException exception) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new GeneralError(exception.getCause().getMessage(), exception.getMessage()));
    }

    /**
     * External exception on microservice string.
     *
     * @param exception
     *            the exception
     * @return the string
     */
    @ExceptionHandler(NoDataFoundException.class)
    public ResponseEntity<GeneralError> externalExceptionOnMicroservice(NoDataFoundException exception) {
        return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(new GeneralError("No data found", exception.getMessage()));
    }

    /**
     * Handle resource not found exception string.
     *
     * @return the string
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<GeneralError> handleResourceNotFoundException() {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new GeneralError("Resource not found", "Resource not found"));
    }

    /**
     * Runtime string.
     *
     * @param exception
     *            the exception
     * @return the string
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<GeneralError> runtime(RuntimeException exception) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new GeneralError("Internal server error", exception.getMessage()));
    }

    /**
     * Runtime string.
     *
     * @param exception
     *            the exception
     * @return the string
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<GeneralError> runtime(MethodArgumentNotValidException exception) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new GeneralError("Bad request", exception.getMessage()));
    }
}
