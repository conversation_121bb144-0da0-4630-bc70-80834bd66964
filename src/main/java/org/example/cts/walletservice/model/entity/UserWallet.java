package org.example.cts.walletservice.model.entity;

import jakarta.persistence.*;

import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

/**
 * The type User wallet.
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class UserWallet {

    @Id
    @UuidGenerator
    @EqualsAndHashCode.Include
    private UUID id;

    private UUID userId;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private Instant createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private Instant updatedAt;

    @OneToMany(mappedBy = "userWallet", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<WalletBalance> balances;

    @OneToMany(mappedBy = "userWallet", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<WalletTransaction> transactions;
}
