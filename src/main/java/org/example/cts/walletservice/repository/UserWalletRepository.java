package org.example.cts.walletservice.repository;

import java.util.Optional;
import java.util.UUID;

import org.example.cts.walletservice.model.entity.UserWallet;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * The interface User wallet repository.
 */
public interface UserWalletRepository extends JpaRepository<UserWallet, UUID> {
    Optional<UserWallet> findByUserId(UUID userId);
}
