package org.example.cts.walletservice.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for interacting with the currency service.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CurrencyServiceClient {

    private final RestTemplate restTemplate;

    @Value("${services.currency.url}")
    private String currencyServiceUrl;

    /**
     * Fetches available currency codes from the currency service.
     *
     * @return a list of currency codes
     */
    public List<String> fetchCurrencyCodes() {
        try {
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

            ResponseEntity<Set<String>> response = restTemplate.exchange(
                currencyServiceUrl + "/v1/currencies/pairs/codes",
                HttpMethod.GET,
                requestEntity,
                new ParameterizedTypeReference<Set<String>>() {}
            );

            Set<String> currencyCodes = response.getBody();
            return currencyCodes != null ? new ArrayList<>(currencyCodes) : List.of();
        } catch (Exception e) {
            throw new RuntimeException("Failed to fetch currency codes", e);
        }
    }
}
