package org.example.cts.walletservice.service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.JsonFormat;

import java.lang.reflect.Type;
import java.time.Instant;
import org.example.cts.walletservice.exception.ProtoMappingException;
import org.springframework.stereotype.Component;

/**
 * The type Proto mapper.
 */
@Component
public class ProtoMapper {

    private final JsonFormat.Printer protoPrinter;
    private final JsonFormat.Parser protoParser;
    private final Gson gson;

    /**
     * Instantiates a new Proto mapper.
     */
    public ProtoMapper() {
        this.protoPrinter = JsonFormat.printer();
        this.protoParser = JsonFormat.parser().ignoringUnknownFields();

        this.gson = new GsonBuilder()
            .registerTypeAdapter(Instant.class, new InstantAdapter())
            .create();
    }

    /**
     * Adapter to convert between Instant and Protobuf-compatible JSON format
     */
    private static class InstantAdapter implements JsonSerializer<Instant>, JsonDeserializer<Instant> {
        @Override
        public JsonElement serialize(Instant src, Type typeOfSrc, JsonSerializationContext context) {
            if (src == null) {
                return null;
            }

            JsonObject obj = new JsonObject();
            obj.addProperty("seconds", src.getEpochSecond());
            obj.addProperty("nanos", src.getNano());
            return obj;
        }

        @Override
        public Instant deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
                throws JsonParseException {
            if (json.isJsonObject()) {
                JsonObject obj = json.getAsJsonObject();
                long seconds = obj.get("seconds").getAsLong();
                int nanos = obj.has("nanos") ? obj.get("nanos").getAsInt() : 0;
                return Instant.ofEpochSecond(seconds, nanos);
            } else if (json.isJsonPrimitive()) {
                return Instant.parse(json.getAsString());
            }
            throw new JsonParseException("Unexpected JSON type for Instant");
        }
    }

    /**
     * From proto t.
     *
     * @param <T>
     *            the type parameter
     * @param protoMessage
     *            the proto message
     * @param dtoClass
     *            the dto class
     * @return the t
     */
    public <T> T fromProto(MessageOrBuilder protoMessage, Class<T> dtoClass) {
        try {
            String json = protoPrinter.print(protoMessage);
            return gson.fromJson(json, dtoClass);
        } catch (InvalidProtocolBufferException e) {
            throw new ProtoMappingException("Failed to convert Proto to DTO");
        }
    }

    /**
     * Converts a Java Instant to a Protobuf Timestamp
     */
    public static Timestamp instantToTimestamp(Instant instant) {
        if (instant == null) {
            return Timestamp.getDefaultInstance();
        }
        return Timestamp.newBuilder()
                .setSeconds(instant.getEpochSecond())
                .setNanos(instant.getNano())
                .build();
    }

    /**
     * Converts a Protobuf Timestamp to a Java Instant
     */
    public static Instant timestampToInstant(Timestamp timestamp) {
        if (timestamp == null || timestamp.equals(Timestamp.getDefaultInstance())) {
            return Instant.EPOCH;
        }
        return Instant.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos());
    }
}
