package org.example.cts.walletservice.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.example.cts.walletservice.exception.NoDataFoundException;
import org.example.cts.walletservice.model.dto.CreateWalletRequest;
import org.example.cts.walletservice.model.dto.UserPortfolioWalletDto;
import org.example.cts.walletservice.model.entity.UserWallet;
import org.example.cts.walletservice.model.entity.WalletBalance;
import org.example.cts.walletservice.repository.UserWalletRepository;
import org.example.cts.walletservice.repository.WalletBalanceRepository;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

/**
 * The type User wallet service.
 */
@Service
@RequiredArgsConstructor
public class UserWalletService {

    private final UserWalletRepository walletRepository;
    private final WalletBalanceRepository balanceRepository;
    private final ModelMapper modelMapper;
    private final CurrencyServiceClient currencyServiceClient;

    /**
     * Create wallet user wallet.
     *
     * @param request
     *            the request
     * @return the user wallet
     */
    @Transactional
    public UserWallet createWallet(CreateWalletRequest request) {
        UserWallet wallet = new UserWallet();
        wallet.setUserId(UUID.fromString(request.getUserId()));

        setupInitialBalances(wallet);

        walletRepository.save(wallet);
        return wallet;
    }

    private void setupInitialBalances(UserWallet wallet) {
        List<String> currencyCodes = fetchUniqueCurrencyCodes();
        wallet.setBalances(new ArrayList<>());
        for (String currencyCode : currencyCodes) {
            WalletBalance balance = new WalletBalance();
            balance.setCurrencyCode(currencyCode);
            balance.setBalance(BigDecimal.ZERO);
            balance.setUserWallet(wallet);
            balanceRepository.save(balance);
            wallet.getBalances().add(balance);
        }

    }

    /**
     * Fetches available currency codes from the currency service
     *
     * @return list of currency codes
     */
    private List<String> fetchUniqueCurrencyCodes() {
        return currencyServiceClient.fetchCurrencyCodes()
                .stream()
                .distinct()
                .toList();
    }

    /**
     * Gets wallet.
     *
     * @param walletId
     *            the wallet id
     * @return the wallet
     */
    public UserWallet getWallet(UUID walletId) {
        return walletRepository.findById(walletId).orElseThrow(() -> new NoDataFoundException("Wallet not found"));
    }

    /**
     * Gets wallet by user id.
     *
     * @param userId
     *            the user id
     * @return the wallet
     */
    public UserPortfolioWalletDto getWalletByUserId(UUID userId) {
        return modelMapper.map(walletRepository.findByUserId(userId).orElseThrow(() -> new NoDataFoundException("Wallet not found")), UserPortfolioWalletDto.class);
    }
}
