package org.example.cts.walletservice.service.grpcService;

import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.example.cts.walletservice.grpc.CreateWalletRequestProto;
import org.example.cts.walletservice.grpc.CreateWalletResponseProto;
import org.example.cts.walletservice.grpc.GetWalletResponseProto;
import org.example.cts.walletservice.grpc.UserWalletProto;
import org.example.cts.walletservice.grpc.UserWalletServiceGrpc;
import org.example.cts.walletservice.grpc.WalletIdProto;
import org.example.cts.walletservice.model.dto.CreateWalletRequest;
import org.example.cts.walletservice.model.dto.WalletId;
import org.example.cts.walletservice.model.entity.UserWallet;
import org.example.cts.walletservice.service.ProtoMapper;
import org.example.cts.walletservice.service.UserWalletService;

/**
 * The type User wallet service grpc.
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class UserWalletServiceGrpcImpl extends UserWalletServiceGrpc.UserWalletServiceImplBase {
    private final UserWalletService userWalletService;
    private final ProtoMapper protoMapper;

    @Override
    public void createWallet(CreateWalletRequestProto request, StreamObserver<CreateWalletResponseProto> responseObserver) {
        try {
            CreateWalletRequest walletRequest = protoMapper.fromProto(request, CreateWalletRequest.class);

            UserWallet wallet = userWalletService.createWallet(walletRequest);

            UserWalletProto walletProto = toWalletProto(wallet);

            CreateWalletResponseProto response = getResponse(CreateWalletResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Wallet created successfully")
                    .setWallet(walletProto));

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Failed to create wallet", e);

            responseObserver.onError(Status.INTERNAL
                    .withDescription("Failed to create wallet: " + e.getMessage())
                    .withCause(e)
                    .asRuntimeException());
        }
    }

    private static CreateWalletResponseProto getResponse(CreateWalletResponseProto.Builder walletProto) {
        return walletProto
                .build();
    }

    @Override
    public void getWallet(WalletIdProto request, StreamObserver<GetWalletResponseProto> responseObserver) {
        try {
            WalletId walletRequest = protoMapper.fromProto(request, WalletId.class);

            UserWallet wallet = userWalletService.getWallet(UUID.fromString(walletRequest.getWalletId()));

            UserWalletProto walletProto = toWalletProto(wallet);

            GetWalletResponseProto response = GetWalletResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Wallet retrieved successfully")
                    .setWallet(walletProto)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Failed to get wallet", e);

            responseObserver.onError(Status.INTERNAL
                    .withDescription("Failed to retrieve wallet: " + e.getMessage())
                    .withCause(e)
                    .asRuntimeException());
        }
    }

    /**
     * Converts a UserWallet entity to a UserWalletProto
     *
     * @param wallet the wallet entity to convert
     * @return the proto representation of the wallet
     */
    private UserWalletProto toWalletProto(UserWallet wallet) {
        return UserWalletProto.newBuilder()
                .setId(wallet.getId().toString())
                .setUserId(wallet.getUserId().toString())
                .setCreatedAt(ProtoMapper.instantToTimestamp(wallet.getCreatedAt()))
                .setUpdatedAt(ProtoMapper.instantToTimestamp(wallet.getUpdatedAt()))
                .build();
    }
}
