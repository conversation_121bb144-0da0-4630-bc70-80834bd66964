package org.example.cts.walletservice.service.grpcService;

import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.example.cts.walletservice.grpc.GetBalanceResponseProto;
import org.example.cts.walletservice.grpc.GetWalletBalancesResponseProto;
import org.example.cts.walletservice.grpc.WalletBalanceProto;
import org.example.cts.walletservice.grpc.WalletBalanceServiceGrpc;
import org.example.cts.walletservice.grpc.WalletIdProto;
import org.example.cts.walletservice.model.dto.BalanceId;
import org.example.cts.walletservice.model.dto.WalletId;
import org.example.cts.walletservice.model.entity.WalletBalance;
import org.example.cts.walletservice.service.ProtoMapper;
import org.example.cts.walletservice.service.WalletBalanceService;

/**
 * The type Wallet balance service grpc.
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class WalletBalanceServiceGrpcImpl extends WalletBalanceServiceGrpc.WalletBalanceServiceImplBase {

    private final WalletBalanceService walletBalanceService;
    private final ProtoMapper protoMapper;

    @Override
    public void getBalance(WalletIdProto request, StreamObserver<GetBalanceResponseProto> responseObserver) {
        try {
            WalletId walletId = protoMapper.fromProto(request, WalletId.class);

            WalletBalance balance = walletBalanceService.getBalance(UUID.fromString(walletId.getWalletId()));

            WalletBalanceProto balanceProto = toBalanceProto(balance);

            GetBalanceResponseProto response = GetBalanceResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Balance retrieved successfully")
                    .setBalance(balanceProto)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("Failed to get balance", e);

            GetBalanceResponseProto response = GetBalanceResponseProto.newBuilder()
                    .setSuccess(false)
                    .setMessage("Failed to retrieve balance: " + e.getMessage())
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getWalletBalances(WalletIdProto request, StreamObserver<GetWalletBalancesResponseProto> responseObserver) {
        try {
            BalanceId balanceId = protoMapper.fromProto(request, BalanceId.class);

            List<WalletBalance> balances = walletBalanceService.getBalances(UUID.fromString(balanceId.walletId()));

            GetWalletBalancesResponseProto.Builder responseBuilder = GetWalletBalancesResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Wallet balances retrieved successfully");

            for (WalletBalance balance : balances) {
                WalletBalanceProto balanceProto = toBalanceProto(balance);
                responseBuilder.addBalances(balanceProto);
            }

            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Failed to get wallet balances", e);

            GetWalletBalancesResponseProto response = GetWalletBalancesResponseProto.newBuilder()
                    .setSuccess(false)
                    .setMessage("Failed to retrieve wallet balances: " + e.getMessage())
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * Converts a WalletBalance entity to a WalletBalanceProto
     *
     * @param balance the balance entity to convert
     * @return the proto representation of the balance
     */
    private WalletBalanceProto toBalanceProto(WalletBalance balance) {
        return WalletBalanceProto.newBuilder()
                .setId(balance.getId().toString())
                .setCurrencyId(balance.getCurrencyCode())
                .setBalance(balance.getBalance().toString())
                .setUpdatedAt(ProtoMapper.instantToTimestamp(balance.getUpdatedAt()))
                .build();
    }
}
