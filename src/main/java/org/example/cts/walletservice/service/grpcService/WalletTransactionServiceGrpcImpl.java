package org.example.cts.walletservice.service.grpcService;

import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.example.cts.walletservice.grpc.*;
import org.example.cts.walletservice.model.dto.CreateTransactionRequest;
import org.example.cts.walletservice.model.dto.TransactionId;
import org.example.cts.walletservice.model.dto.WalletId;
import org.example.cts.walletservice.model.entity.WalletTransaction;
import org.example.cts.walletservice.service.ProtoMapper;
import org.example.cts.walletservice.service.WalletTransactionService;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class WalletTransactionServiceGrpcImpl extends WalletTransactionServiceGrpc.WalletTransactionServiceImplBase {

    private final WalletTransactionService transactionService;
    private final ProtoMapper protoMapper;

    @Override
    public void getTransaction(TransactionIdProto request, StreamObserver<GetTransactionResponseProto> responseObserver) {
        try {
            TransactionId transactionId = protoMapper.fromProto(request, TransactionId.class);

            WalletTransaction transaction = transactionService
                    .getTransaction(UUID.fromString(transactionId.getTransactionId()));

            WalletTransactionProto transactionProto = toTransactionProto(transaction);

            GetTransactionResponseProto response = GetTransactionResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Transaction retrieved successfully")
                    .setTransaction(transactionProto)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Failed to get transaction", e);

            GetTransactionResponseProto response = GetTransactionResponseProto.newBuilder()
                    .setSuccess(false)
                    .setMessage("Failed to retrieve transaction: " + e.getMessage())
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    @Override
    public void createTransaction(CreateTransactionRequestProto request,
            StreamObserver<CreateTransactionResponseProto> responseObserver) {
        try {
            CreateTransactionRequest ctr = protoMapper.fromProto(request, CreateTransactionRequest.class);

            WalletTransaction transaction = transactionService.createTransaction(ctr);

            WalletTransactionProto transactionProto = toTransactionProto(transaction);

            CreateTransactionResponseProto response = CreateTransactionResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Transaction created successfully")
                    .setTransaction(transactionProto)
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Failed to create transaction", e);

            CreateTransactionResponseProto response = CreateTransactionResponseProto.newBuilder()
                    .setSuccess(false)
                    .setMessage("Failed to create transaction: " + e.getMessage())
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getWalletTransactions(WalletIdProto request,
            StreamObserver<GetWalletTransactionsResponseProto> responseObserver) {
        try {
            WalletId walletId = protoMapper.fromProto(request, WalletId.class);

            List<WalletTransaction> transactions = transactionService
                    .getTransactions(UUID.fromString(walletId.getWalletId()));

            GetWalletTransactionsResponseProto.Builder responseBuilder = GetWalletTransactionsResponseProto.newBuilder()
                    .setSuccess(true)
                    .setMessage("Wallet transactions retrieved successfully");

            for (WalletTransaction transaction : transactions) {
                WalletTransactionProto transactionProto = toTransactionProto(transaction);
                responseBuilder.addTransactions(transactionProto);
            }

            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Failed to get wallet transactions", e);

            GetWalletTransactionsResponseProto response = GetWalletTransactionsResponseProto.newBuilder()
                    .setSuccess(false)
                    .setMessage("Failed to retrieve wallet transactions: " + e.getMessage())
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * Converts a WalletTransaction entity to a WalletTransactionProto
     *
     * @param transaction the transaction entity to convert
     * @return the proto representation of the transaction
     */
    private WalletTransactionProto toTransactionProto(WalletTransaction transaction) {
        return WalletTransactionProto
                .newBuilder()
                .setId(transaction.getId().toString())
                .setTimestamp(ProtoMapper.instantToTimestamp(transaction.getTimestamp()))
                .setType(transaction.getType().name())
                .setCurrencyId(transaction.getCurrencyId().toString())
                .setTradingPair(transaction.getTradingPair())
                .setAmount(transaction.getAmount().toString())
                .setBalanceAfter(transaction.getBalanceAfter().toString())
                .setFee(transaction.getFee().toString())
                .setDescription(transaction.getDescription())
                .build();
    }
}
