syntax = "proto3";

package com.example.wallet;

option java_multiple_files = true;
option java_package = "org.example.cts.walletservice.grpc";
option java_outer_classname = "WalletServiceProto";

import "google/protobuf/timestamp.proto";

// Service for managing user wallets
service UserWalletService {
  // Create a new wallet
  rpc CreateWallet(CreateWalletRequestProto) returns (CreateWalletResponseProto);

  // Get wallet by ID
  rpc GetWallet(WalletIdProto) returns (GetWalletResponseProto);
}

// Service for managing wallet balances
service WalletBalanceService {
  // Get a specific balance by ID
  rpc GetBalance(WalletIdProto) returns (GetBalanceResponseProto);

  // Get all balances for a wallet
  rpc GetWalletBalances(WalletIdProto) returns (GetWalletBalancesResponseProto);
}

// Service for managing wallet transactions
service WalletTransactionService {
  // Get a specific transaction by ID
  rpc GetTransaction(TransactionIdProto) returns (GetTransactionResponseProto);

  // Create a new transaction
  rpc CreateTransaction(CreateTransactionRequestProto) returns (CreateTransactionResponseProto);

  // Get all transactions for a wallet
  rpc GetWalletTransactions(WalletIdProto) returns (GetWalletTransactionsResponseProto);
}

message TransactionIdProto{
  string transactionId = 1;
}

// A users wallet
message UserWalletProto{
  string id = 1;
  string userId = 2;
  google.protobuf.Timestamp createdAt = 3;
  google.protobuf.Timestamp updatedAt = 4;
  repeated WalletBalanceProto balances = 5;
  repeated WalletTransactionProto transactions = 6;
}

message ListWalletBalanceProto{
  repeated WalletBalanceProto balances = 1;
}

// A users balance of currency
message WalletBalanceProto{
  string id = 1;
  string currencyId = 2;
  string balance = 3;
  google.protobuf.Timestamp updatedAt = 4;
}

message ListWalletTransactionProto{
  repeated WalletTransactionProto transactions = 1;
}

// A users transaction
message WalletTransactionProto {
  string id = 1;
  google.protobuf.Timestamp timestamp = 2;
  string type = 3;
  string currencyId = 4;
  string tradingPair = 5;
  string amount = 6;
  string balanceAfter = 7;
  string fee = 8;
  string description = 9;
}

// Request to create a wallet
message CreateWalletRequestProto {
  string userId = 1;
}

// Response for create wallet
message CreateWalletResponseProto {
  bool success = 1;
  string message = 2;
  UserWalletProto wallet = 3;
}

// Request of wallet by ID
message WalletIdProto {
  string walletId = 1;
}

// Response for get wallet
message GetWalletResponseProto {
  bool success = 1;
  string message = 2;
  UserWalletProto wallet = 3;
}

// Response for get balance
message GetBalanceResponseProto {
  bool success = 1;
  string message = 2;
  WalletBalanceProto balance = 3;
}

// Response for get wallet balances
message GetWalletBalancesResponseProto {
  bool success = 1;
  string message = 2;
  repeated WalletBalanceProto balances = 3;
}

// Response for get transaction
message GetTransactionResponseProto {
  bool success = 1;
  string message = 2;
  WalletTransactionProto transaction = 3;
}

// Response for create transaction
message CreateTransactionResponseProto {
  bool success = 1;
  string message = 2;
  WalletTransactionProto transaction = 3;
}

// Response for get wallet transactions
message GetWalletTransactionsResponseProto {
  bool success = 1;
  string message = 2;
  repeated WalletTransactionProto transactions = 3;
}

// Request to create a transaction
message CreateTransactionRequestProto {
  string walletId = 1;
  string currencyId = 2;
  string type = 3;
  string amount = 4;
  string fee = 5;
  string tradingPair = 6;
  string description = 7;
  google.protobuf.Timestamp createdAt = 8;
}