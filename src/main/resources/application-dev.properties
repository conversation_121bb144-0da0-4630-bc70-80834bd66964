spring.application.name=wallet-service

server.port=8092
grpc.server.port=9093

spring.datasource.url=${WALLET_DB_URL:******************************************}
spring.datasource.username=${WALLET_DB_USER:postgres}
spring.datasource.password=${WALLET_DB_PASSWORD:12345}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=true

eureka.client.service-url.defaultZone=http://localhost:8761/eureka/
eureka.instance.prefer-ip-address=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.hostname=${spring.application.name}

# Currency service configuration
services.currency.url=${CURRENCY_SERVICE_URL:http://localhost:8080}
services.currency.auth.cookie.name=ACCESS_TOKEN